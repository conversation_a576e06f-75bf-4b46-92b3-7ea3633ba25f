{"name": "chillbaby", "version": "1.0.5", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "postinstall": "patch-package", "release": "cd ./android && ./gradlew clean && ./gradlew assemblerelease", "debug": "cd ./android && ./gradlew clean && ./gradlew assembledebug", "appdebug": "ENV=chillbaby && cd android && ./gradlew clean && ENVFILE=.env  && ./gradlew assembleDebug", "apprelease": "ENV=chillbaby && cd android && ./gradlew clean && ENVFILE=.env ./gradlew assembleRelease", "bundle": "ENV=chillbaby && cd ./android && ./gradlew clean && ENVFILE=.env  ./gradlew bundleRelease", "docs": "jsdoc -c jsdoc.conf.json"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.23.1", "@react-native-clipboard/clipboard": "react-native-clipboard/clipboard", "@react-native-community/datetimepicker": "^7.7.0", "@react-native-community/netinfo": "^11.3.1", "@react-native-community/push-notification-ios": "^1.11.0", "@react-native-firebase/app": "^19.2.2", "@react-native-firebase/messaging": "^19.2.2", "@react-native-masked-view/masked-view": "^0.3.1", "@react-native-picker/picker": "^2.7.5", "@react-navigation/bottom-tabs": "^6.5.20", "@react-navigation/drawer": "^6.6.15", "@react-navigation/native": "^6.1.17", "@react-navigation/stack": "^6.3.29", "@twotalltotems/react-native-otp-input": "^1.3.11", "@types/convert-string": "^0.1.3", "add": "^2.0.6", "convert-string": "^0.1.0", "i18n-js": "^3.8.0", "lodash": "^4.17.21", "moment": "^2.30.1", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "react": "18.2.0", "react-native": "0.73.6", "react-native-action-button": "^2.8.5", "react-native-android-open-settings": "^1.3.0", "react-native-android-wifi": "^0.0.41", "react-native-animatable": "^1.4.0", "react-native-app-intro-slider": "^4.0.4", "react-native-background-timer": "^2.4.1", "react-native-ble-manager": "^11.5.3", "react-native-ble-plx": "^3.1.2", "react-native-bluetooth-state-manager": "^1.3.5", "react-native-camera": "^4.2.1", "react-native-carplay": "^2.4.1-beta.0", "react-native-code-push": "^8.2.2", "react-native-config": "^1.5.1", "react-native-countdown-circle-timer": "^3.2.1", "react-native-country-picker-modal": "^2.0.0", "react-native-date-picker": "^5.0.2", "react-native-deck-swiper": "^2.0.17", "react-native-device-info": "^10.13.1", "react-native-document-picker": "^9.1.1", "react-native-event-listeners": "^1.0.7", "react-native-fast-image": "^8.6.3", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "2.21.0", "react-native-get-location": "^4.0.1", "react-native-image-crop-picker": "^0.41.6", "react-native-in-app-notification": "^3.2.0", "react-native-inappbrowser-reborn": "^3.7.0", "react-native-iphone-x-helper": "^1.3.1", "react-native-linear-gradient": "^2.8.3", "react-native-permissions": "^4.1.5", "react-native-progress": "^5.0.1", "react-native-push-notification": "^8.1.1", "react-native-qrcode-scanner": "^1.5.5", "react-native-qrcode-svg": "^6.3.0", "react-native-reanimated": "3.14.0", "react-native-render-html": "^6.3.4", "react-native-restart": "^0.0.27", "react-native-safe-area-context": "^4.9.0", "react-native-screens": "3.33.0", "react-native-share": "^10.1.0", "react-native-simple-toast": "^3.3.1", "react-native-splash-screen": "^3.3.0", "react-native-svg": "^15.1.0", "react-native-switch": "^1.5.1", "react-native-torch": "^1.2.0", "react-native-vector-icons": "^10.0.3", "react-native-video": "^5.2.1", "react-native-webview": "^13.8.5", "react-native-wifi-reborn": "^4.12.1", "react-redux": "^8.0.5", "redux": "^4.2.0", "redux-persist": "^6.0.0", "redux-thunk": "^2.4.2", "rn-fetch-blob": "^0.12.0", "sails.io.js": "^1.2.1", "socket.io-client": "^2.0.3", "toggle-switch-react-native": "^3.3.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.73.21", "@react-native/eslint-config": "0.73.2", "@react-native/metro-config": "0.73.5", "@react-native/typescript-config": "0.73.1", "@types/react": "^18.2.6", "@types/react-native-push-notification": "^8.1.4", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}